"""
* Copyright (c) 2025 LayerNext, Inc.
* all rights reserved.
* This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
* app.py will act as the main script which handle the fast api backend regarding LLM integration

* @class LlmFastAPIApp
* @description This class represents the main FastAPI application for the LLM (Language Model) service.
* <AUTHOR>
"""

import os
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
from typing import Optional
from services.api_config_service import APIConfigService

from controllers.chat_controller import router as chat_router
from controllers.auth_controller import router as auth_router
from controllers.agent_controller import router as agent_router
from controllers.media_controller import router as media_router
from controllers.insight_controller import router as insight_router
from controllers.file_upload_controller import router as file_upload_router
from controllers.public_controller import router as public_router
from controllers.internal_controller import router as internal_router, InternalController
from controllers.insight_dashboard_controller import router as insight_dashboard_router

from services.scheduler_service import SchedulerService
from utils.package_install_utils import PackageInstallUtils
from utils.logger import get_debug_logger
import socketio

ON_START_PIP_DISABLED = os.environ.get("ON_START_PIP_DISABLED")

logger = get_debug_logger("app", "./logs/server.log")


class LlmFastAPIApp:
    def __init__(self):
        # Load API configurations before initializing FastAPI
        self._load_api_configs()

        # 1️⃣ Create FastAPI app
        self.app = FastAPI()
        # 2️⃣ Create Socket.IO server (ASGI mode)
        self.sio = socketio.AsyncServer(async_mode="asgi", cors_allowed_origins=["*"])  # limit in production
        # 3️⃣ Create ASGI wrapper
        sio_app = socketio.ASGIApp(self.sio, socketio_path="socket.io")

        # 4️⃣ Mount under /ws (or /socket.io)
        self.app.mount("/ws", sio_app)

        self.setup_middlewares()
        self.setup_routers()
        self.app.add_event_handler("startup", self.on_startup)

        # 5️⃣ Register Socket.IO event handlers
        self._register_socketio_handlers()

    def _load_api_configs(self):
        """Load API configurations from database before other initializations"""
        api_config_service = APIConfigService()
        success, message = api_config_service.load_config()
        if success:
            logger.info("Successfully loaded API configurations")
        else:
            logger.warning(f"Failed to load API configurations: {message}")

    def setup_middlewares(self):
        """
        Description: Configures CORS middleware to handle Cross-Origin Resource Sharing.
        """
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )

    def setup_routers(self):
        """
        Description: Includes the chat and authentication routers in the FastAPI app.
        """
        self.app.include_router(chat_router)
        self.app.include_router(auth_router)
        self.app.include_router(agent_router)
        self.app.include_router(media_router)
        self.app.include_router(file_upload_router)
        self.app.include_router(public_router)
        self.app.include_router(insight_router)
        self.app.include_router(internal_router)
        self.app.include_router(insight_dashboard_router)

    def start_scheduler_service(self):
        """
        Description: Starts the scheduler service.
        """
        self.scheduler_service = SchedulerService()

    def update_db_with_pip_packages(self):
        """
        Handle pip packages installation on startup
        """
        install_pip = PackageInstallUtils()
        install_pip.install_pip_packages_on_start()

    def on_startup(self):
        """
        Recommended to start background tasks in a FastAPI event handler, like on_startup, to ensure all app components are fully initialized and ready.
        """
        # create files, memory_dumps, storage if not exist
        if not os.path.exists("./files"):
            os.makedirs("./files")
        if not os.path.exists("./memory_dumps"):
            os.makedirs("./memory_dumps")
        if not os.path.exists("./storage/files"):
            os.makedirs("./storage/files")

        install_pip_disabled: bool = True if ON_START_PIP_DISABLED == "True" else False

        # Check if pip install is disabled on start
        if not install_pip_disabled:
            self.update_db_with_pip_packages()

        self.start_scheduler_service()

    def _register_socketio_handlers(self):
        @self.sio.event
        async def connect(sid, environ):
            print(f"Socket connected: {sid}")

        @self.sio.event
        async def disconnect(sid):
            print(f"Socket disconnected: {sid}")

        @self.sio.event
        async def message(sid, data):
            print(f"Message from {sid}: {data}")
            await self.sio.emit("message", f"Server got: {data}", to=sid)


def run():
    """
    Description: Runs the FastAPI application.

    Raises:
        - ValueError: If the APP_PORT environment variable is not a valid integer.
    """
    APP_PORT = os.environ.get("APP_PORT")
    try:
        port = int(APP_PORT)
    except ValueError:
        raise ValueError("APP_PORT environment variable must be an integer")

    llm_fastapi_app = LlmFastAPIApp()
    uvicorn.run(llm_fastapi_app.app, host="0.0.0.0", port=port)


if __name__ == "__main__":
    run()
