{"tableName": "ServiceTypes", "description": "The ServiceTypes table catalogs all unique service offerings, detailing each with a unique Service_Type_ID, description, base charge, average completion time, and estimated time range. It standardizes service definitions for consistent use across pricing, scheduling, and reporting, and is referenced by both ServiceRecords and Invoices to associate services with transactions and operational metrics.", "fields": [{"name": "Service_Type_ID", "dataType": "int", "description": "A unique integer identifier for each service type, ensuring no two services share the same ID, with values ranging from 1 to 99.", "availableValues": ["1", "2", "3", "4"], "is_unstructured": false}, {"name": "Description", "dataType": "<PERSON><PERSON><PERSON>(255)", "description": "A textual description of the service type, providing clarity on the specific service offered, such as 'Tyre Change' or 'Brake Repair'.", "availableValues": ["Tyre Change", "Oil Change", "<PERSON><PERSON><PERSON>", "Car Wash"], "totalDistinctValueCount": 4, "is_unstructured": false}, {"name": "base_charge", "dataType": "float", "description": "The starting fee associated with the service type, represented as a floating-point number, which varies uniquely across services.", "availableValues": ["25", "60", "100", "150"], "is_unstructured": false}, {"name": "Average_Time_Taken", "dataType": "float", "description": "An average time in minutes that indicates how long it typically takes to complete the service, ensuring clarity in service duration expectations.", "availableValues": ["15", "20", "30", "60"], "is_unstructured": false}, {"name": "Time_Range", "dataType": "<PERSON><PERSON><PERSON>(255)", "description": "A descriptive range of time, in minutes, that provides an estimated duration for service completion, aiding in customer service planning.", "availableValues": ["20-40", "10-20", "45-90", "15-30"], "totalDistinctValueCount": 4, "is_unstructured": false}]}