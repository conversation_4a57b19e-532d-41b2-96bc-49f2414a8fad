{"tableName": "ServiceRecords", "description": "The ServiceRecords table stores detailed records of individual services performed, uniquely identified by Service_ID. Each entry links to a customer, an employee, and a service type, and records the date and duration of the service. The table enables tracking of service history, analysis of employee activity, and categorization by service type through relationships with the ServiceTypes table.", "fields": [{"name": "Service_ID", "dataType": "int", "description": "A unique integer that identifies each service record, ensuring distinct tracking and referencing of individual service entries.", "subsetOfAvailableValues": ["1", "2", "3", "4", "5", "and 270722 more..."], "is_unstructured": false}, {"name": "Customer_ID", "dataType": "int", "description": "An integer that links to the associated customer's profile, allowing for the grouping of multiple service records under the same customer.", "subsetOfAvailableValues": ["1", "2", "4", "6", "7", "and 17407 more..."], "is_unstructured": false}, {"name": "Employee_ID", "dataType": "int", "description": "An integer identifier for the employee who performed the service, enabling tracking of employee performance and service responsibilities.", "subsetOfAvailableValues": ["1", "2", "3", "4", "6", "and 52 more..."], "is_unstructured": false}, {"name": "Service_Type_ID", "dataType": "int", "description": "An integer that categorizes the service provided, linking to predefined service types in the ServiceTypes table.", "availableValues": ["1", "2", "3", "4"], "is_unstructured": false}, {"name": "Time_Taken", "dataType": "float", "description": "A positive floating-point number representing the duration of the service in hours or minutes, allowing for analysis of service efficiency.", "subsetOfAvailableValues": ["14.25", "14.2501", "14.2502", "14.2503", "14.2504", "and 261607 more..."], "is_unstructured": false}, {"name": "Service_Date", "dataType": "date", "description": "The date when the service was performed, recorded in a date format to track service history and assist in scheduling.", "subsetOfAvailableValues": ["2019-01-06", "2019-01-09", "2019-01-10", "2019-01-08", "2019-01-13", "and 2350 more..."], "is_unstructured": false}], "relationships": [{"child_table": "ServiceRecords", "parent_table": "ServiceTypes", "key_column_mapping": [{"parent_column": "Service_Type_ID", "child_column": "Service_Type_ID"}], "relationship_type": "one-to-many"}, {"child_table": "ServiceRecords", "parent_table": "EmployeeData", "key_column_mapping": [{"parent_column": "Employee_ID", "child_column": "Employee_ID"}], "relationship_type": "one-to-many"}]}