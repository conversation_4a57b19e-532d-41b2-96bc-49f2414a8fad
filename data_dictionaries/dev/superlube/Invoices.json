{"tableName": "Invoices", "description": "The Invoices table stores detailed billing records for services rendered to customers, uniquely identified by Invoice_ID and Service_ID. It links to ServiceTypes and ServiceRecords for service classification and details, and to CustomerData for customer association. Key fields include invoice date and total charge, supporting service tracking, financial reporting, and revenue analysis.", "fields": [{"name": "Invoice_ID", "dataType": "int", "description": "A unique integer identifier for each invoice in the table, serving as the primary key to distinctly reference each record.", "subsetOfAvailableValues": ["1", "2", "3", "4", "5", "and 271030 more..."], "is_unstructured": false}, {"name": "Service_Type_ID", "dataType": "int", "description": "An integer that categorizes the type of service billed in the invoice, linking to the ServiceTypes table and limited to values under 100.", "availableValues": ["1", "2", "3", "4"], "is_unstructured": false}, {"name": "Customer_ID", "dataType": "int", "description": "An integer that identifies the customer related to the invoice, establishing a relationship with the CustomerData table, indicating multiple invoices per customer.", "subsetOfAvailableValues": ["1", "2", "4", "6", "7", "and 17423 more..."], "is_unstructured": false}, {"name": "Service_ID", "dataType": "int", "description": "A unique integer identifier for the specific service rendered, serving as a foreign key to the ServiceRecords table, ensuring each service is distinctly referenced.", "subsetOfAvailableValues": ["1", "2", "3", "4", "5", "and 271030 more..."], "is_unstructured": false}, {"name": "Invoice_Date", "dataType": "date", "description": "The date when the invoice was issued, crucial for tracking service delivery timing and for financial reporting.", "subsetOfAvailableValues": ["2019-01-06", "2019-01-09", "2019-01-10", "2019-01-08", "2019-01-13", "and 2352 more..."], "is_unstructured": false}, {"name": "Total_Charge", "dataType": "float", "description": "A floating-point number indicating the total billed amount for services on the invoice, essential for revenue tracking and financial analysis.", "subsetOfAvailableValues": ["22.5", "22.5001", "22.5002", "22.5003", "22.5004", "and 266698 more..."], "is_unstructured": false}], "relationships": [{"child_table": "Invoices", "parent_table": "CustomerData", "key_column_mapping": [{"parent_column": "Customer_ID", "child_column": "Customer_ID"}], "relationship_type": "one-to-many"}, {"child_table": "Invoices", "parent_table": "ServiceRecords", "key_column_mapping": [{"parent_column": "Service_ID", "child_column": "Service_ID"}], "relationship_type": "one-to-one"}, {"child_table": "Invoices", "parent_table": "ServiceTypes", "key_column_mapping": [{"parent_column": "Service_Type_ID", "child_column": "Service_Type_ID"}], "relationship_type": "one-to-many"}]}