{"tableName": "EmployeeData", "description": "EmployeeData contains a unique record for each employee, identified by Employee_ID. It captures the employee’s name, job role, associated service type (Service_Type_ID, which may be null and references ServiceTypes), hire date, and leave date (if applicable). This table supports analysis of employee roles, tenure, workforce composition, and links employees to specific service categories within the organization.", "fields": [{"name": "Employee_ID", "dataType": "int", "description": "A unique integer identifier for each employee, ensuring that no two records share the same ID, critical for referencing individual employee records.", "subsetOfAvailableValues": ["1", "2", "3", "4", "6", "and 66 more..."], "is_unstructured": false}, {"name": "Name", "dataType": "<PERSON><PERSON><PERSON>(255)", "description": "The full name of the employee, stored as a variable character string, used for identifying employees in reports and queries.", "subsetOfAvailableValues": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "and 66 more..."], "totalDistinctValueCount": 71, "is_unstructured": false}, {"name": "Role", "dataType": "<PERSON><PERSON><PERSON>(255)", "description": "A variable character string representing the employee's job role, which can include 'Technician', 'Customer Service', or 'Manager', providing insight into role distribution.", "availableValues": ["Technician", "Manager", "Customer Service"], "totalDistinctValueCount": 3, "is_unstructured": false}, {"name": "Service_Type_ID", "dataType": "int", "description": "An integer categorizing the type of service associated with the employee, with values ranging from 1 to 4, essential for understanding the services offered and may contain missing values.", "availableValues": ["1", "2", "3", "4"], "is_unstructured": false}, {"name": "Hire_Date", "dataType": "date", "description": "The date on which the employee was hired, significant for tracking employment duration and calculating tenure within the company.", "subsetOfAvailableValues": ["2019-01-01", "2023-05-28", "2025-06-05", "2022-01-04", "2020-11-05", "and 61 more..."], "is_unstructured": false}, {"name": "Leave_Date", "dataType": "date", "description": "The date an employee leaves the company, applicable to less than half of the records, indicating the employee's departure status and tenure.", "subsetOfAvailableValues": ["2025-02-13", "2024-07-28", "2023-06-23", "2022-11-05", "2025-04-21", "and 7 more..."], "is_unstructured": false}], "relationships": [{"child_table": "EmployeeData", "parent_table": "ServiceTypes", "key_column_mapping": [{"parent_column": "Service_Type_ID", "child_column": "Service_Type_ID"}], "relationship_type": "one-to-many"}]}