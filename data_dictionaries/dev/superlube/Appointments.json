{"tableName": "Appointments", "description": "The Appointments table stores details of scheduled service appointments, each uniquely identified by Appointment_ID. It records the associated customer (Customer_ID), vehicle (Vehicle_ID), requested service type (Service_Type_ID, linked to ServiceTypes), scheduled appointment date (Appointment_Date), and booking date (Schedule_Date). This table enables tracking of customer and vehicle service histories, scheduling trends, and analysis of service types requested.", "fields": [{"name": "Appointment_ID", "dataType": "int", "description": "A unique integer identifier for each appointment record, serving as the primary key and ensuring distinct appointments in the table.", "subsetOfAvailableValues": ["1", "2", "3", "4", "5", "and 272125 more..."], "is_unstructured": false}, {"name": "Customer_ID", "dataType": "int", "description": "An integer representing the unique identifier for a customer, establishing a one-to-many relationship with the CustomerData table, allowing multiple appointments per customer.", "subsetOfAvailableValues": ["1", "2", "4", "6", "7", "and 17480 more..."], "is_unstructured": false}, {"name": "Vehicle_ID", "dataType": "<PERSON><PERSON><PERSON>(50)", "description": "A string that uniquely identifies the vehicle associated with the appointment, facilitating a one-to-many relationship with the CustomerData table for tracking multiple appointments per vehicle.", "subsetOfAvailableValues": ["VA1022", "VA1047", "VA1048", "VA1061", "VA1075", "and 17480 more..."], "totalDistinctValueCount": 17485, "is_unstructured": false}, {"name": "Service_Type_ID", "dataType": "int", "description": "An integer categorizing the type of service requested for the appointment, linked to the ServiceTypes table and restricted to positive integers under 100.", "availableValues": ["1", "2", "3", "4"], "is_unstructured": false}, {"name": "Appointment_Date", "dataType": "date", "description": "A date field indicating the scheduled date for the service appointment, essential for planning service delivery.", "subsetOfAvailableValues": ["2019-01-01", "2019-01-02", "2019-01-03", "2019-01-04", "2019-01-05", "and 2355 more..."], "is_unstructured": false}, {"name": "Schedule_Date", "dataType": "date", "description": "A date field representing when the appointment was booked, which may differ from the Appointment_Date, thus allowing flexibility in scheduling.", "subsetOfAvailableValues": ["2019-01-06", "2019-01-09", "2019-01-10", "2019-01-08", "2019-01-13", "and 2366 more..."], "is_unstructured": false}], "relationships": [{"child_table": "Appointments", "parent_table": "CustomerData", "key_column_mapping": [{"parent_column": "Customer_ID", "child_column": "Customer_ID"}], "relationship_type": "one-to-many"}, {"child_table": "Appointments", "parent_table": "CustomerData", "key_column_mapping": [{"parent_column": "Vehicle_ID", "child_column": "Vehicle_ID"}], "relationship_type": "one-to-many"}, {"child_table": "Appointments", "parent_table": "ServiceTypes", "key_column_mapping": [{"parent_column": "Service_Type_ID", "child_column": "Service_Type_ID"}], "relationship_type": "one-to-many"}]}