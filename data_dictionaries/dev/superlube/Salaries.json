{"tableName": "Salaries", "description": "The Salaries table tracks individual salary payments to employees, with each record uniquely identified by Salary_ID. It includes Employee_ID to associate payments with employees, Pay_Date for the payment date, and Amount for the salary paid. This structure enables historical analysis of employee compensation and payroll management over time.", "fields": [{"name": "Salary_ID", "dataType": "int", "description": "A unique integer identifier for each salary record, serving as the primary key for the Salaries table to ensure distinct tracking of salary entries.", "subsetOfAvailableValues": ["1", "2", "3", "4", "5", "and 2708 more..."], "is_unstructured": false}, {"name": "Employee_ID", "dataType": "int", "description": "An integer that uniquely identifies the employee associated with the salary record, allowing for multiple entries per employee over time to reflect salary history.", "subsetOfAvailableValues": ["1", "2", "3", "4", "6", "and 64 more..."], "is_unstructured": false}, {"name": "Pay_Date", "dataType": "date", "description": "The date on which the salary payment was issued, essential for payroll management and tracking historical payment schedules.", "subsetOfAvailableValues": ["2019-01-31", "2019-02-28", "2019-03-31", "2019-04-30", "2019-05-31", "and 72 more..."], "is_unstructured": false}, {"name": "Amount", "dataType": "float", "description": "A floating-point number representing the total salary amount paid to the employee on the corresponding Pay_Date, reflecting individual salary payments.", "subsetOfAvailableValues": ["2500", "2800", "3000", "3200", "3500", "and 1 more..."], "is_unstructured": false}], "relationships": [{"child_table": "Salaries", "parent_table": "EmployeeData", "key_column_mapping": [{"parent_column": "Employee_ID", "child_column": "Employee_ID"}], "relationship_type": "one-to-many"}]}