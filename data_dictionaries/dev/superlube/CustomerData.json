{"tableName": "CustomerData", "description": "The CustomerData table contains one record per customer, identified by a unique Customer_ID. It includes each customer’s full name, contact information, join date, and details of their associated vehicle (Vehicle_ID, make, and model). This table supports customer identification, communication, and vehicle-related analysis, with Customer_ID ensuring uniqueness.", "fields": [{"name": "Customer_ID", "dataType": "int", "description": "A unique integer identifier assigned to each customer, serving as the primary key to ensure distinct records within the CustomerData table.", "subsetOfAvailableValues": ["1", "2", "4", "6", "7", "and 18397 more..."], "is_unstructured": false}, {"name": "Name", "dataType": "<PERSON><PERSON><PERSON>(255)", "description": "A string representing the full name of the customer, used primarily for identification and communication purposes, though not guaranteed to be unique.", "subsetOfAvailableValues": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "and 16567 more..."], "totalDistinctValueCount": 16572, "is_unstructured": false}, {"name": "Contact_Information", "dataType": "<PERSON><PERSON><PERSON>(255)", "description": "A string that contains the customer's email address or other contact details, typically unique for each customer to facilitate effective communication.", "subsetOfAvailableValues": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "and 16567 more..."], "totalDistinctValueCount": 16572, "is_unstructured": false}, {"name": "Join_Date", "dataType": "date", "description": "A date indicating when the customer registered with the company, valuable for analyzing customer lifecycle and engagement trends.", "subsetOfAvailableValues": ["2024-11-21", "2019-09-02", "2021-11-18", "2022-12-20", "2020-03-10", "and 2352 more..."], "is_unstructured": false}, {"name": "Vehicle_ID", "dataType": "<PERSON><PERSON><PERSON>(50)", "description": "A unique string identifier for each vehicle linked to a customer, ensuring distinct entries for vehicle records within the database.", "subsetOfAvailableValues": ["VA1022", "VA1047", "VA1048", "VA1061", "VA1065", "and 18397 more..."], "totalDistinctValueCount": 18402, "is_unstructured": false}, {"name": "Vehicle_Make", "dataType": "<PERSON><PERSON><PERSON>(100)", "description": "A string that specifies the manufacturer of the vehicle owned by the customer, categorized by brands such as Nissan, Toyota, and Ford.", "availableValues": ["Ford", "Honda", "Toyota", "Nissan", "Chevrolet"], "totalDistinctValueCount": 5, "is_unstructured": false}, {"name": "Vehicle_Model", "dataType": "<PERSON><PERSON><PERSON>(100)", "description": "A string that details the specific model of the customer's vehicle, including types such as Coupe, Sedan, SUV, and Truck.", "availableValues": ["Hatchback", "Coupe", "SUV", "Truck", "Sedan"], "totalDistinctValueCount": 5, "is_unstructured": false}]}