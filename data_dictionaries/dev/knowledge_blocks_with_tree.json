{"knowledge_blocks": [{"purpose": "Generate a monthly gross-profit percentage relative to labour costs (and accompanying bar chart) for the most recent six complete months.", "business_rules": ["Gross-profit percentage = (Revenue – LabourCost) / LabourCost * 100, rounded to two decimals.", "Revenue is the monthly sum of Invoices.Total_Charge using Invoice_Date.", "LabourCost is the monthly sum of Salaries.Amount using Pay_Date.", "Define month_start as the first day of each month via DATE_TRUNC('month', <date>).", "Only include months where LabourCost is not null and greater than zero (complete months).", "Select the latest six months that satisfy the above completeness rule; if fewer than six exist in the last 6-month window, widen the look-back (e.g., 12 months) until six complete months are found.", "Order result chronologically (ascending month_start)."], "linked_knowledge_block_references": [], "sql_query_logic": "1. Aggregate revenue per month from superlube.Invoices.\n2. Aggregate labour_cost per month from superlube.Salaries.\n3. FULL JOIN or UNION both aggregates on month_start.\n4. Limit raw pull to the last 12 (or more) calendar months to ensure sufficient complete data.\n5. After retrieval, let downstream processing filter for completeness and choose the latest six valid months.", "sql": "WITH rev AS (\n  SELECT DATE_TRUNC('month', Invoice_Date) AS month_start,\n         SUM(Total_Charge)            AS revenue\n  FROM superlube.Invoices\n  WHERE Invoice_Date >= DATE_TRUNC('month', CURRENT_DATE - INTERVAL '11 months')\n  GROUP BY 1\n),\nlab AS (\n  SELECT DATE_TRUNC('month', Pay_Date) AS month_start,\n         SUM(Amount)                   AS labour_cost\n  FROM superlube.Salaries\n  WHERE Pay_Date >= DATE_TRUNC('month', CURRENT_DATE - INTERVAL '11 months')\n  GROUP BY 1\n)\nSELECT COALESCE(rev.month_start, lab.month_start) AS month_start,\n       revenue,\n       labour_cost\nFROM rev\nFULL JOIN lab USING (month_start)\nORDER BY month_start;", "data_processing_logic": "load data; filter = rows where labour_cost != null and labour_cost > 0; sort by month_start ascending; take last 6 rows; compute gross_profit_pct = round(((revenue - labour_cost) / labour_cost) * 100, 2); output CSV [month_start YYYY-MM, revenue, labour_cost, gross_profit_pct]; build bar chart gross_profit_pct vs month_start.", "donts": "Do NOT include months with null or zero labour_cost (will give division errors and mislead percentage). Do NOT assume the last six calendar months are complete; always verify labour_cost data. Do NOT compute gross-profit percentage using revenue as denominator; use labour_cost.", "additional_details": "Output artefacts: CSV file (gross_profit_pct_by_month.csv) and bar chart (gross_profit_pct_bar.png). This block assumes superlube schema names; adjust if schema aliases change.", "block_id": "finance_monthly_rev_labour_agg_1", "pattern_keys": ["finance_monthly_rev_labour_agg", "finance_monthly_gprofit_pct_trend"]}, {"purpose": "Produce monthly utilisation metrics (available hours, used hours, utilisation %) for each service type over the most recent six-month window.", "business_rules": ["Consider only employees with Role = 'Technician'.", "Used hours per month & service type = SUM(ServiceRecords.Time_Taken) ÷ 60 (convert minutes to hours).", "Determine technician_count per service type from EmployeeData (Role = 'Technician').", "Available hours per month & service type = technician_count × weeks_in_month × 40 (standard 40-hour work-week).", "Date window = first day of month 5 months ago up to first day of next month (six complete months).", "Include all service types from ServiceTypes, even if no usage in a month (report zero used hours).", "Round utilisation percentage to two decimals; if available_hours = 0, return 0 to avoid division error."], "linked_knowledge_block_references": ["finance_monthly_salary_trend_1"], "sql_query_logic": "1. params CTE defines start_month (CURRENT_DATE – 5 months, truncated to month) and end_month (first day of next month).\n2. months CTE collects distinct month_start values from ServiceRecords within window.\n3. service_types CTE pulls all Service_Type_ID & Description.\n4. used_hours CTE aggregates Time_Taken (÷60) by month_start & Service_Type_ID for technicians.\n5. tech_counts CTE counts technicians per Service_Type_ID.\n6. availability CTE crosses months with service_types, joins tech_counts, and computes available_hours = weeks_in_month × 40 × tech_count (weeks via DATEDIFF('week', month_start, month_start + 1 month)).\n7. Final SELECT joins availability to used_hours and service_types; calculates utilisation %.", "sql": "WITH params AS (\n  SELECT DATE_TRUNC('month', DATEADD(month, -5, CURRENT_DATE)) AS start_month,\n         DATE_TRUNC('month', DATEADD(month,  1, CURRENT_DATE)) AS end_month\n),\nmonths AS (\n  SELECT DISTINCT DATE_TRUNC('month', sr.Service_Date) AS month_start\n  FROM superlube.ServiceRecords sr, params p\n  WHERE sr.Service_Date >= p.start_month AND sr.Service_Date < p.end_month\n),\nservice_types AS (\n  SELECT Service_Type_ID, Description FROM superlube.ServiceTypes\n),\nused_hours AS (\n  SELECT DATE_TRUNC('month', sr.Service_Date) AS month_start,\n         sr.Service_Type_ID,\n         SUM(sr.Time_Taken)/60.0 AS used_hours\n  FROM superlube.ServiceRecords sr\n  JOIN params p ON sr.Service_Date >= p.start_month AND sr.Service_Date < p.end_month\n  WHERE sr.Employee_ID IN (SELECT Employee_ID FROM superlube.EmployeeData WHERE Role = 'Technician')\n  GROUP BY 1, sr.Service_Type_ID\n),\ntech_counts AS (\n  SELECT ed.Service_Type_ID, COUNT(*) AS tech_count\n  FROM superlube.EmployeeData ed\n  WHERE ed.Role = 'Technician'\n  GROUP BY ed.Service_Type_ID\n),\navailability AS (\n  SELECT m.month_start,\n         st.Service_Type_ID,\n         tc.tech_count,\n         DATEDIFF('week', m.month_start, DATEADD(month, 1, m.month_start)) * 40 * tc.tech_count AS available_hours\n  FROM months m\n  CROSS JOIN service_types st\n  JOIN tech_counts tc ON tc.Service_Type_ID = st.Service_Type_ID\n)\nSELECT st.Description                       AS Service_Type,\n       TO_CHAR(a.month_start,'YYYY-MM')     AS Month,\n       a.available_hours                    AS Total_Available_Hours,\n       COALESCE(u.used_hours,0)             AS Total_Used_Hours,\n       ROUND(CASE WHEN a.available_hours=0 THEN 0 ELSE COALESCE(u.used_hours,0)/a.available_hours*100 END,2) AS Utilization_Percentage\nFROM availability a\nLEFT JOIN used_hours u ON a.month_start = u.month_start AND a.Service_Type_ID = u.Service_Type_ID\nJOIN service_types st ON st.Service_Type_ID = a.Service_Type_ID\nORDER BY a.month_start, st.Description;", "data_processing_logic": "1. Execute SQL and save result.\n2. If tool returns .dat, export unchanged to CSV (monthly_service_utilization_6mo.csv).\n3. Present table in answer and optionally highlight key insights.", "donts": "• Do not include non-technician roles when counting technicians or summing used hours.\n• Do not forget to convert Time_Taken (minutes) to hours before aggregation.\n• Avoid dividing by zero when available_hours = 0; default utilisation to 0.\n• Do not omit service types with zero usage; cross-join months with all service types to give full grid.", "additional_details": "Weeks_in_month is calculated via DATEDIFF('week', month_start, month_start + 1 month), ensuring correct handling of partial weeks at month boundaries. Adjust schema names (superlube.*) if environment differs.", "block_id": "operations_service_utilization_trend_1", "pattern_keys": ["operations_service_utilization_trend"]}, {"purpose": "Compute and present the monthly trend of total salary payments for a specified calendar year.", "business_rules": ["Use Salaries.Pay_Date as the reference date for payment timing.", "Sum Salaries.Amount; all records are considered valid because the dataset contains no void/reversal status flags.", "Report results at a monthly granularity, ordered chronologically."], "linked_knowledge_block_references": [], "sql_query_logic": "Aggregate salaries by month for the target year, using an inclusive lower-bound and exclusive upper-bound date filter to capture the full 12-month window.", "sql": "SELECT\n    DATE_TRUNC('month', Pay_Date) AS Salary_Month,\n    SUM(Amount)              AS Total_Salary\nFROM Salaries\nWHERE Pay_Date >= '{YEAR}-01-01'\n  AND Pay_Date <  '{YEAR_PLUS_1}-01-01'\nGROUP BY Salary_Month\nORDER BY Salary_Month ASC;", "data_processing_logic": "1. Load SQL result into DataFrame sorted by Salary_Month.\n2. Add column Month = format(Salary_Month, 'YYYY-MM').\n3. Calculate MoM_Diff  = Total_Salary.diff().\n4. Calculate MoM_Pct_Change = (MoM_Diff / Total_Salary.shift(1)) * 100.\n5. Output table/CSV with columns: Month, Total_Salary, MoM_Diff, MoM_Pct_Change.\n6. Optionally generate line chart of Total_Salary over Month.", "donts": "• Do NOT filter on non-existent status or reversal columns.\n• Do NOT assume data exists for every month; handle missing months gracefully.\n• Avoid hard-coding the year—parameterize '{YEAR}'.", "additional_details": "Salaries table links to EmployeeData via Employee_ID; joins are unnecessary for overall trend but may be used for employee-level filtering when required. If only partial year data exists, clarify this limitation in the narrative.", "block_id": "finance_monthly_salary_trend_1", "pattern_keys": ["finance_monthly_salary_trend", "finance_monthly_rev_labour_agg"]}], "knowledge_tree": {"Operations": [{"pattern": "Monthly service utilization trend analysis", "pattern_key": "operations_service_utilization_trend"}], "Finance": [{"pattern": "Monthly revenue and labour cost aggregation", "pattern_key": "finance_monthly_rev_labour_agg"}, {"pattern": "Monthly gross-profit percentage trend analysis", "pattern_key": "finance_monthly_gprofit_pct_trend"}, {"pattern": "Monthly salary payment trend analysis", "pattern_key": "finance_monthly_salary_trend"}]}}