// Native WebSocket client test (no Socket.IO)
// This connects directly to a WebSocket endpoint without Socket.IO protocol

const WebSocket = require('ws');

// Test connecting to different WebSocket endpoints
const endpoints = [
    'ws://localhost:5082/ws',
    'ws://localhost:5082/websocket',
    'ws://localhost:5082/ws/conversation/'
];

function testWebSocketConnection(url) {
    console.log(`\n🔌 Attempting to connect to: ${url}`);
    
    const ws = new WebSocket(url);
    
    ws.on('open', function open() {
        console.log(`✅ Connected to ${url}`);

        // Send different types of test messages based on the endpoint
        if (url.includes('/ws/conversation/')) {
            // Test conversation endpoint with various message types
            setTimeout(() => {
                const pingMessage = JSON.stringify({
                    type: 'ping',
                    timestamp: new Date().toISOString()
                });
                ws.send(pingMessage);
                console.log(`📤 Sent ping: ${pingMessage}`);
            }, 1000);

            setTimeout(() => {
                const echoMessage = JSON.stringify({
                    type: 'echo',
                    message: 'Test echo message',
                    timestamp: new Date().toISOString()
                });
                ws.send(echoMessage);
                console.log(`📤 Sent echo: ${echoMessage}`);
            }, 2000);

            setTimeout(() => {
                const broadcastMessage = JSON.stringify({
                    type: 'broadcast_request',
                    message: 'Hello to all connected clients!',
                    timestamp: new Date().toISOString()
                });
                ws.send(broadcastMessage);
                console.log(`📤 Sent broadcast request: ${broadcastMessage}`);
            }, 3000);

        } else {
            // Send a simple test message for other endpoints
            const testMessage = JSON.stringify({
                type: 'test',
                message: 'Hello from native WebSocket client',
                timestamp: new Date().toISOString()
            });

            ws.send(testMessage);
            console.log(`📤 Sent: ${testMessage}`);
        }
    });
    
    ws.on('message', function message(data) {
        const messageStr = data.toString();
        console.log(`📥 Received from ${url}:`, messageStr);

        // Try to parse and display analysis step progress messages nicely
        try {
            const messageObj = JSON.parse(messageStr);
            if (messageObj.data_type === 'progress_bar' && messageObj.data && messageObj.data.data) {
                const progressData = messageObj.data.data;
                console.log(`🔄 Analysis Progress Update:`);
                console.log(`   📋 Title: ${progressData.title}`);
                console.log(`   📊 Progress: ${progressData.progress}%`);
                console.log(`   🔧 Current Step: ${progressData.currentStep}`);
                console.log(`   🌊 Streaming: ${progressData.isStreaming}`);
                console.log(`   💬 Conversation: ${messageObj.conversation_id}`);
                console.log(`   🎯 Session: ${messageObj.session_id}`);
            }
        } catch (e) {
            // Not JSON or different format, already logged above
        }
    });
    
    ws.on('error', function error(err) {
        console.log(`❌ Error connecting to ${url}:`, err.message);
    });
    
    ws.on('close', function close(code, reason) {
        console.log(`🔌 Connection to ${url} closed. Code: ${code}, Reason: ${reason}`);
    });
    
    // Close connection after 5 seconds
    setTimeout(() => {
        if (ws.readyState === WebSocket.OPEN) {
            ws.close();
        }
    }, 5000);
}

// Test each endpoint
console.log('🚀 Starting native WebSocket connection tests...');
endpoints.forEach((endpoint, index) => {
    setTimeout(() => {
        testWebSocketConnection(endpoint);
    }, index * 1000); // Stagger the connection attempts
});

// Keep the process alive for a bit
setTimeout(() => {
    console.log('\n✨ Test completed');
    process.exit(0);
}, 10000);
