// Native WebSocket client test (no Socket.IO)
// This connects directly to a WebSocket endpoint without Socket.IO protocol

const WebSocket = require('ws');

// Test connecting to different WebSocket endpoints
const endpoints = [
    'ws://localhost:5082/ws',
    'ws://localhost:5082/websocket',
    'ws://localhost:5082/ws/websocket'
];

function testWebSocketConnection(url) {
    console.log(`\n🔌 Attempting to connect to: ${url}`);
    
    const ws = new WebSocket(url);
    
    ws.on('open', function open() {
        console.log(`✅ Connected to ${url}`);
        
        // Send a test message
        const testMessage = JSON.stringify({
            type: 'test',
            message: 'Hello from native WebSocket client',
            timestamp: new Date().toISOString()
        });
        
        ws.send(testMessage);
        console.log(`📤 Sent: ${testMessage}`);
    });
    
    ws.on('message', function message(data) {
        console.log(`📥 Received from ${url}:`, data.toString());
    });
    
    ws.on('error', function error(err) {
        console.log(`❌ Error connecting to ${url}:`, err.message);
    });
    
    ws.on('close', function close(code, reason) {
        console.log(`🔌 Connection to ${url} closed. Code: ${code}, Reason: ${reason}`);
    });
    
    // Close connection after 5 seconds
    setTimeout(() => {
        if (ws.readyState === WebSocket.OPEN) {
            ws.close();
        }
    }, 5000);
}

// Test each endpoint
console.log('🚀 Starting native WebSocket connection tests...');
endpoints.forEach((endpoint, index) => {
    setTimeout(() => {
        testWebSocketConnection(endpoint);
    }, index * 1000); // Stagger the connection attempts
});

// Keep the process alive for a bit
setTimeout(() => {
    console.log('\n✨ Test completed');
    process.exit(0);
}, 10000);
