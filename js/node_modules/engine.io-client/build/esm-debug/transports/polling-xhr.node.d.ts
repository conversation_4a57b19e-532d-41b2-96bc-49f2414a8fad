import { BaseXHR, Request } from "./polling-xhr.js";
/**
 * HTTP long-polling based on the `XMLHttpRequest` object provided by the `xmlhttprequest-ssl` package.
 *
 * Usage: Node.js, <PERSON><PERSON> (compat), <PERSON><PERSON> (compat)
 *
 * @see https://developer.mozilla.org/en-US/docs/Web/API/XMLHttpRequest
 */
export declare class XHR extends BaseXHR {
    request(opts?: Record<string, any>): Request;
}
