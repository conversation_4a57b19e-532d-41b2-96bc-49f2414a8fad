import { io } from "socket.io-client";
const socket = io("https://chat.dev.layernext.ai", {transports: ["websocket"] });
//const socket = io("http://localhost:5082", {transports: ["websocket"] });

socket.on("connect", () => {
  console.log("✅ Socket.IO Connected:", socket.id);

  // Emit connect_conversation message
  socket.emit("connect_conversation", {
    type: "connect_conversation",
    client_id: socket.id,
    timestamp: new Date().toISOString(),
    message: "Client connected and ready to receive analysis progress"
  });

  console.log("📡 Sent connect_conversation message to server");
  console.log("🔄 Listening for analysis progress messages...");
  console.log("📝 Run analysis queries or python test_websocket_analysis_steps.py to see progress updates");
  console.log("📝 Press Ctrl+C to exit\n");
});

socket.on("message", (msg) => {
  console.log("💬 Server message:", msg);
});

// Listen for connect_conversation acknowledgment
socket.on("connect_conversation_ack", (data) => {
  console.log("✅ Server acknowledged connection:", data.message);
  console.log(`   🆔 Server ID: ${data.server_id}`);
  console.log(`   ⏰ Timestamp: ${data.timestamp}`);
  console.log(`   📊 Status: ${data.status}\n`);
});

// Listen for analysis progress messages
socket.on("analysis_progress", (data) => {
  const timestamp = new Date().toLocaleTimeString();

  if (data.data_type === 'progress_bar' && data.data && data.data.data) {
    const progressData = data.data.data;

    console.log(`\n🔄 [${timestamp}] Analysis Progress Update:`);
    console.log(`   📋 Title: ${progressData.title}`);

    if (progressData.progress !== undefined) {
      const progressBar = '█'.repeat(Math.floor(progressData.progress/5)) + '░'.repeat(20-Math.floor(progressData.progress/5));
      console.log(`   📊 Progress: ${progressData.progress}% [${progressBar}]`);
    }

    console.log(`   🔧 Current Step: ${progressData.currentStep}`);
    console.log(`   🌊 Streaming: ${progressData.isStreaming ? '✅' : '❌'}`);
    console.log(`   💬 Conversation: ${data.conversation_id}`);
    console.log(`   🎯 Session: ${data.session_id}`);
    console.log(`   📝 Session Type: ${data.session_type}`);
    console.log(`   ⚡ Status: ${data.status}`);
  } else {
    console.log(`\n📨 [${timestamp}] Other analysis message:`, data);
  }
});

socket.on("disconnect", () => {
  console.log("❌ Socket.IO Disconnected");
});

socket.on("reconnect", () => {
  console.log("🔄 Socket.IO Reconnected:", socket.id);

  // Emit connect_conversation message on reconnection
  socket.emit("connect_conversation", {
    type: "connect_conversation",
    client_id: socket.id,
    timestamp: new Date().toISOString(),
    message: "Client reconnected and ready to receive analysis progress"
  });

  console.log("📡 Sent connect_conversation message after reconnection");
});

socket.on("connect_error", (error) => {
  console.log("🚫 Connection Error:", error.message);
});

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n👋 Shutting down Socket.IO client...');
  socket.disconnect();
  process.exit(0);
});

