import { io } from "socket.io-client";
const socket = io("http://localhost:5082", {transports: ["websocket"] });

socket.on("connect", () => {
  console.log("✅ Socket.IO Connected:", socket.id);
  socket.emit("message", "Hello from Socket.IO client");
  console.log("🔄 Listening for analysis progress messages...");
  console.log("📝 Run analysis queries or python test_websocket_analysis_steps.py to see progress updates");
  console.log("📝 Press Ctrl+C to exit\n");
});

socket.on("message", (msg) => {
  console.log("💬 Server message:", msg);
});

// Listen for analysis progress messages
socket.on("analysis_progress", (data) => {
  const timestamp = new Date().toLocaleTimeString();

  if (data.data_type === 'progress_bar' && data.data && data.data.data) {
    const progressData = data.data.data;

    console.log(`\n🔄 [${timestamp}] Analysis Progress Update:`);
    console.log(`   📋 Title: ${progressData.title}`);

    if (progressData.progress !== undefined) {
      const progressBar = '█'.repeat(Math.floor(progressData.progress/5)) + '░'.repeat(20-Math.floor(progressData.progress/5));
      console.log(`   📊 Progress: ${progressData.progress}% [${progressBar}]`);
    }

    console.log(`   🔧 Current Step: ${progressData.currentStep}`);
    console.log(`   🌊 Streaming: ${progressData.isStreaming ? '✅' : '❌'}`);
    console.log(`   💬 Conversation: ${data.conversation_id}`);
    console.log(`   🎯 Session: ${data.session_id}`);
    console.log(`   📝 Session Type: ${data.session_type}`);
    console.log(`   ⚡ Status: ${data.status}`);
  } else {
    console.log(`\n📨 [${timestamp}] Other analysis message:`, data);
  }
});

socket.on("disconnect", () => {
  console.log("❌ Socket.IO Disconnected");
});

socket.on("connect_error", (error) => {
  console.log("🚫 Connection Error:", error.message);
});

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n👋 Shutting down Socket.IO client...');
  socket.disconnect();
  process.exit(0);
});

