# WebSocket Analysis Step Progress Testing

This document explains how to test the WebSocket functionality for analysis step progress updates.

## Overview

The system now sends real-time progress updates to all connected WebSocket clients whenever analysis steps are flowing during ongoing questions. The message format follows this structure:

```json
{
  "conversation_id": "<chat_id>",
  "session_id": "<session_id>",
  "status": "in_progress",
  "session_type": "complex_analysis",
  "data_type": "progress_bar",
  "data": {
    "type": "progress_bar",
    "data": {
      "title": "<Question title>",
      "progress": 30,
      "currentStep": "<analysis step string>",
      "isStreaming": true
    }
  }
}
```

## Components Added

### 1. WebSocket Service (`services/websocket_service.py`)
- Manages WebSocket connections
- Provides methods to broadcast messages to all connected clients
- Includes `push_analysis_step_progress()` method for analysis updates

### 2. WebSocket Endpoint (`/ws/conversation/`)
- Native WebSocket endpoint (not Socket.IO)
- Handles connection management through WebSocketService
- Accepts connections at `ws://localhost:5082/ws/conversation/`

### 3. Analysis Step Integration
- Modified `active_session_send_analysis_step()` in `models/conversation.py`
- Automatically sends WebSocket messages when analysis steps are triggered
- Includes progress estimation based on step type

## Testing Steps

### 1. Start the FastAPI Server
```bash
python app.py
```

### 2. Connect WebSocket Test Client
```bash
cd js
npm install ws  # if not already installed
node test_native_websocket.js
```

### 3. Test Analysis Step Simulation
```bash
python test_websocket_analysis_steps.py
```

### 4. Test with Real Analysis
Start a real conversation that triggers analysis steps (complex analysis session).

## Expected Behavior

1. **WebSocket Connection**: Client connects to `/ws/conversation/` and receives welcome message
2. **Analysis Steps**: When analysis steps are triggered, all connected clients receive progress updates
3. **Progress Updates**: Messages include:
   - Conversation and session IDs
   - Current step description
   - Progress percentage (estimated)
   - Question title
   - Streaming status

## Message Types

### Analysis Step Progress
```json
{
  "conversation_id": "conv_123",
  "session_id": "session_456", 
  "status": "in_progress",
  "session_type": "complex_analysis",
  "data_type": "progress_bar",
  "data": {
    "type": "progress_bar",
    "data": {
      "title": "Sales Analysis Query",
      "progress": 50,
      "currentStep": "Generating SQL",
      "isStreaming": true
    }
  }
}
```

### Connection Established
```json
{
  "type": "connection_established",
  "connection_id": "uuid-here",
  "message": "Successfully connected to WebSocket server",
  "timestamp": 1234567890
}
```

## Progress Mapping

The system estimates progress based on analysis step types:

- `metadata_lookup`: 10%
- `data_locate`: 20%
- `table_analyse`: 30%
- `column_finding`: 40%
- `sql_generation`: 50%
- `sql_review`: 60%
- `code`: 70%
- `code_review`: 75%
- `execute`: 80%
- `data_review`: 85%
- `answer_prepare`: 90%
- `reasoning_and_planning`: 25%
- `agentic_flow`: 65%
- `data_processing`: 75%

## Troubleshooting

### WebSocket Connection Issues
- Ensure FastAPI server is running
- Check that `/ws/conversation/` endpoint is accessible
- Verify no firewall blocking WebSocket connections

### No Messages Received
- Confirm analysis steps are being triggered in conversations
- Check server logs for WebSocket service errors
- Verify client is properly connected and listening

### Error Handling
- WebSocket errors are logged but don't break analysis flow
- Failed connections are automatically removed from active connections
- Service uses singleton pattern to maintain state across requests

## Integration Points

The WebSocket messages are automatically sent from:
- `models/conversation.py` - `active_session_send_analysis_step()`
- Any code that calls analysis step methods
- Services that trigger complex analysis workflows

No additional code changes needed in existing analysis agents - the WebSocket integration is transparent and automatic.
