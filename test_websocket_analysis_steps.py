#!/usr/bin/env python3
"""
Test script to simulate analysis steps and verify WebSocket messages are sent
"""

import asyncio
import time
from services.websocket_service import WebSocketService
from utils.constant import AnalysisStep

async def test_analysis_step_websocket():
    """
    Test function to simulate analysis steps and send WebSocket messages
    """
    print("🚀 Starting WebSocket Analysis Step Test...")
    
    # Get WebSocket service instance
    websocket_service = WebSocketService()
    
    # Test data
    conversation_id = "test_conv_123"
    session_id = "test_session_456"
    session_type = "complex_analysis"
    question_title = "Test Analysis: Sales Performance Review"
    
    # Simulate different analysis steps
    analysis_steps = [
        (AnalysisStep.METADATA_LOOKUP, 10),
        (AnalysisStep.DATA_SECTION_LOCATE, 20),
        (AnalysisStep.TABLE_ANALYSE, 30),
        (AnalysisStep.COLUMN_FINDING, 40),
        (AnalysisStep.SQL_GENERATION, 50),
        (AnalysisStep.SQL_REVIEW, 60),
        (AnalysisStep.CODE, 70),
        (AnalysisStep.EXECUTE, 80),
        (AnalysisStep.DATA_REVIEW, 90),
        (AnalysisStep.ANSWER_PREPARE, 95),
    ]
    
    print(f"📊 Will simulate {len(analysis_steps)} analysis steps...")
    print(f"🔗 Conversation ID: {conversation_id}")
    print(f"🎯 Session ID: {session_id}")
    print(f"📋 Question: {question_title}")
    print()
    
    for i, (step, progress) in enumerate(analysis_steps):
        print(f"Step {i+1}/{len(analysis_steps)}: {step.description} ({progress}%)")
        
        # Send the analysis step progress
        await websocket_service.push_analysis_step_progress(
            conversation_id=conversation_id,
            session_id=session_id,
            session_type=session_type,
            question_title=question_title,
            current_step=step.description,
            progress=progress
        )
        
        # Wait a bit between steps to simulate real analysis
        await asyncio.sleep(2)
    
    print("\n✅ Analysis step simulation completed!")
    print(f"📈 Total active WebSocket connections: {websocket_service.get_connection_count()}")

async def test_direct_websocket_messages():
    """
    Test function to send various WebSocket message types
    """
    print("\n🔧 Testing direct WebSocket messages...")
    
    websocket_service = WebSocketService()
    
    # Test broadcast message
    await websocket_service.broadcast_to_all({
        "type": "test_broadcast",
        "message": "Hello from test script!",
        "timestamp": time.time()
    })
    
    # Test conversation update
    await websocket_service.push_conversation_update("test_conv_123", {
        "status": "test_update",
        "message": "This is a test conversation update"
    })
    
    print("✅ Direct WebSocket messages sent!")

async def main():
    """
    Main test function
    """
    print("🌐 WebSocket Analysis Step Test Suite")
    print("=" * 50)
    
    try:
        # Test analysis step messages
        await test_analysis_step_websocket()
        
        # Wait a bit
        await asyncio.sleep(1)
        
        # Test other message types
        await test_direct_websocket_messages()
        
        print("\n🎉 All tests completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("📝 Note: Make sure your FastAPI server is running with WebSocket endpoint at /ws/conversation/")
    print("📝 Note: Connect your WebSocket test client before running this script")
    print()
    
    # Run the test
    asyncio.run(main())
