"""
* Copyright (c) 2025 LayerNext, Inc.
* all rights reserved.
* This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
* websocket_service.py handles WebSocket connections and message broadcasting

* @class WebSocketService
* @description This class manages WebSocket connections and provides methods to send messages to connected clients
* <AUTHOR> Assistant
"""

import asyncio
import json
import uuid
from typing import Dict, Set, Optional, Any, Union
from fastapi import WebSocket
from utils.logger import get_debug_logger
from services.singleton_meta import SingletonMeta

logger = get_debug_logger("websocket_service", "./logs/websocket.log")


class WebSocketService(metaclass=SingletonMeta):
    """
    Service class to manage WebSocket connections and handle message broadcasting
    """

    def __init__(self):
        # Dictionary to store active WebSocket connections
        # Key: connection_id (UUID), Value: WebSocket instance
        self.active_connections: Dict[str, WebSocket] = {}

        # Set to track connection IDs for quick lookup
        self.connection_ids: Set[str] = set()

        # Socket.IO server instance (will be set from app.py)
        self.socketio_server = None

        logger.info("WebSocketService initialized")

    def set_socketio_server(self, socketio_server):
        """
        Set the Socket.IO server instance for broadcasting

        Args:
            socketio_server: The Socket.IO AsyncServer instance
        """
        self.socketio_server = socketio_server
        logger.info("Socket.IO server instance set in WebSocketService")

    async def connect(self, websocket: WebSocket) -> str:
        """
        Register a new WebSocket connection

        Args:
            websocket: The WebSocket connection instance

        Returns:
            str: Unique connection ID
        """
        connection_id = str(uuid.uuid4())

        self.active_connections[connection_id] = websocket
        self.connection_ids.add(connection_id)

        logger.info(f"WebSocket connection registered: {connection_id}")
        logger.info(f"Total active connections: {len(self.active_connections)}")

        # Send welcome message to the newly connected client
        welcome_message = {
            "type": "connection_established",
            "connection_id": connection_id,
            "message": "Successfully connected to WebSocket server",
            "timestamp": asyncio.get_event_loop().time(),
        }

        await self.send_to_connection(connection_id, welcome_message)

        return connection_id

    async def disconnect(self, connection_id: str):
        """
        Remove a WebSocket connection

        Args:
            connection_id: The connection ID to remove
        """
        if connection_id in self.active_connections:
            del self.active_connections[connection_id]
            self.connection_ids.discard(connection_id)

            logger.info(f"WebSocket connection removed: {connection_id}")
            logger.info(f"Total active connections: {len(self.active_connections)}")
        else:
            logger.warning(f"Attempted to remove non-existent connection: {connection_id}")

    async def send_to_connection(self, connection_id: str, message: Any):
        """
        Send a message to a specific WebSocket connection

        Args:
            connection_id: The target connection ID
            message: The message to send (dict will be JSON serialized)
        """
        if connection_id not in self.active_connections:
            logger.warning(f"Cannot send message to non-existent connection: {connection_id}")
            return False

        websocket = self.active_connections[connection_id]

        try:
            # Convert message to JSON string if it's a dict
            if isinstance(message, dict):
                message_text = json.dumps(message)
            else:
                message_text = str(message)

            await websocket.send_text(message_text)
            logger.debug(f"Message sent to connection {connection_id}: {message_text}")
            return True

        except Exception as e:
            logger.error(f"Failed to send message to connection {connection_id}: {str(e)}")
            # Remove the connection if it's no longer valid
            await self.disconnect(connection_id)
            return False

    async def broadcast_to_all(self, message: Any):
        """
        Send a message to all connected WebSocket clients and Socket.IO clients

        Args:
            message: The message to broadcast (dict will be JSON serialized)
        """
        total_connections = len(self.active_connections)
        socketio_available = self.socketio_server is not None

        if total_connections == 0 and not socketio_available:
            logger.info("No active connections to broadcast to")
            return

        logger.info(f"Broadcasting message to {total_connections} WebSocket connections and Socket.IO clients")

        # Broadcast to native WebSocket connections
        websocket_tasks = []
        if total_connections > 0:
            for connection_id in list(self.active_connections.keys()):
                task = self.send_to_connection(connection_id, message)
                websocket_tasks.append(task)

        # Broadcast to Socket.IO connections
        socketio_task = None
        if socketio_available:
            socketio_task = self._broadcast_to_socketio(message)

        # Execute all send operations concurrently
        all_tasks = websocket_tasks + ([socketio_task] if socketio_task else [])
        if all_tasks:
            results = await asyncio.gather(*all_tasks, return_exceptions=True)

            websocket_successful = sum(1 for result in results[: len(websocket_tasks)] if result is True)
            socketio_successful = (
                1 if socketio_task and len(results) > len(websocket_tasks) and results[-1] is True else 0
            )

            logger.info(
                f"Broadcast completed: {websocket_successful}/{len(websocket_tasks)} WebSocket, {socketio_successful}/1 Socket.IO"
            )

    async def _broadcast_to_socketio(self, message: Any):
        """
        Broadcast message to all Socket.IO clients

        Args:
            message: The message to broadcast
        """
        try:
            if self.socketio_server:
                # Convert message to appropriate format for Socket.IO
                if isinstance(message, dict):
                    await self.socketio_server.emit("analysis_progress", message)
                else:
                    await self.socketio_server.emit("analysis_progress", {"data": str(message)})
                return True
        except Exception as e:
            logger.error(f"Failed to broadcast to Socket.IO clients: {str(e)}")
            return False

    async def handle_message(self, connection_id: str, message_text: str):
        """
        Handle incoming message from a WebSocket client

        Args:
            connection_id: The connection ID that sent the message
            message_text: The received message text
        """
        try:
            # Try to parse as JSON
            message_data = json.loads(message_text)
            message_type = message_data.get("type", "unknown")

            logger.info(f"Handling message type '{message_type}' from connection {connection_id}")

            # Handle different message types
            if message_type == "ping":
                await self.send_to_connection(
                    connection_id, {"type": "pong", "timestamp": asyncio.get_event_loop().time()}
                )

            elif message_type == "echo":
                await self.send_to_connection(
                    connection_id,
                    {
                        "type": "echo_response",
                        "original_message": message_data,
                        "timestamp": asyncio.get_event_loop().time(),
                    },
                )

            elif message_type == "broadcast_request":
                # Broadcast a message to all connected clients
                broadcast_message = {
                    "type": "broadcast",
                    "from_connection": connection_id,
                    "message": message_data.get("message", "No message content"),
                    "timestamp": asyncio.get_event_loop().time(),
                }
                await self.broadcast_to_all(broadcast_message)

            else:
                # Default response for unknown message types
                await self.send_to_connection(
                    connection_id,
                    {
                        "type": "response",
                        "message": f"Received message of type: {message_type}",
                        "original_message": message_data,
                        "timestamp": asyncio.get_event_loop().time(),
                    },
                )

        except json.JSONDecodeError:
            # Handle non-JSON messages
            logger.info(f"Received non-JSON message from connection {connection_id}: {message_text}")
            await self.send_to_connection(
                connection_id,
                {
                    "type": "text_response",
                    "message": f"Server received: {message_text}",
                    "timestamp": asyncio.get_event_loop().time(),
                },
            )

    def get_connection_count(self) -> int:
        """
        Get the number of active connections

        Returns:
            int: Number of active connections
        """
        return len(self.active_connections)

    def get_connection_ids(self) -> Set[str]:
        """
        Get all active connection IDs

        Returns:
            Set[str]: Set of active connection IDs
        """
        return self.connection_ids.copy()

    async def push_conversation_update(self, conversation_id: str, update_data: Dict[str, Any]):
        """
        Push conversation updates to all connected clients

        Args:
            conversation_id: The conversation ID
            update_data: The update data to send
        """
        message = {
            "type": "conversation_update",
            "conversation_id": conversation_id,
            "data": update_data,
            "timestamp": asyncio.get_event_loop().time(),
        }

        await self.broadcast_to_all(message)
        logger.info(f"Pushed conversation update for conversation {conversation_id} to all clients")

    async def push_analysis_step_progress(
        self,
        conversation_id: str,
        session_id: str,
        session_type: str,
        question_title: str,
        current_step: str,
        progress: int = -1,
    ):
        """
        Push analysis step progress updates to all connected clients

        Args:
            conversation_id: The conversation ID (chat_id)
            session_id: The session ID
            session_type: The session type (e.g., 'complex_analysis')
            question_title: The title of the question being analyzed
            current_step: The current analysis step description
            progress: Progress percentage (default: -1, indicating not applicable)
        """
        message = {
            "conversation_id": conversation_id,
            "session_id": session_id,
            "status": "in_progress",
            "session_type": session_type,
            "data_type": "progress_bar",
            "data": {
                "type": "progress_bar",
                "data": {
                    "title": question_title,
                    "currentStep": current_step,
                    "isStreaming": True,
                },
            },
        }
        if progress >= 0:
            message["data"]["data"]["progress"] = progress

        await self.broadcast_to_all(message)
        logger.info(f"Pushed analysis step progress for conversation {conversation_id}, step: {current_step}")
