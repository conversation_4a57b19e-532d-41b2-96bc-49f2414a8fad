#!/usr/bin/env python3
"""
Test script for the new /users/login endpoint
This script demonstrates how to use the new login endpoint
"""

import requests
import json
import os
from dotenv import load_dotenv

load_dotenv()


def test_login_endpoint():
    """
    Test the /users/login endpoint
    """
    # Get the base URL from environment or use default
    base_url = os.getenv("API_BASE_URL", "http://localhost:5082")
    login_url = f"{base_url}/users/login"

    # Test credentials
    test_credentials = {"email": "<EMAIL>", "password": "q6%N#5Qtvm#V"}

    try:
        print(f"Testing login endpoint: {login_url}")
        print(f"Credentials: {test_credentials['email']}")

        # Make the login request
        response = requests.post(login_url, json=test_credentials, headers={"Content-Type": "application/json"})

        print(f"Response Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")

        if response.status_code == 200:
            login_result = response.json()
            print("Login successful!")
            print(f"Token received: {login_result.get('token', 'No token')[:50]}...")
            print(f"User info: {login_result.get('user', 'No user info')}")
        else:
            print("Login failed!")
            try:
                error_detail = response.json()
                print(f"Error details: {error_detail}")
            except:
                print(f"Error response: {response.text}")

    except requests.exceptions.ConnectionError:
        print("Error: Could not connect to the server. Make sure the server is running.")
    except requests.exceptions.RequestException as e:
        print(f"Request error: {e}")
    except Exception as e:
        print(f"Unexpected error: {e}")


if __name__ == "__main__":
    test_login_endpoint()
